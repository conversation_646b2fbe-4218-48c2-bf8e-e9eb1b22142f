<?php

namespace App;

use App\Observers\ProductObserver;
use App\Services\ProductSalesService;
use Illuminate\Support\Facades\DB;
use Laravel\Scout\Searchable;
use Spatie\Tags\HasTags;
use App\Traits\SaleTrait;
use Illuminate\Support\Str;
use App\Traits\MutatorTrait;
use App\Traits\RecurringTrait;
use App\Traits\DigitalMediaTrait;
use App\Traits\OnlinePriceTrait;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Outl1ne\MenuBuilder\Models\MenuItem;

class Product extends Model implements HasMedia
{
    use InteractsWithMedia, HasTags, SaleTrait, OnlinePriceTrait;
    use DigitalMediaTrait;
    use RecurringTrait;
    use MutatorTrait;
    use Searchable;

    protected $with = [
        'media',
        'vendor',
        'filters',
        'creators',
        'categories',
        'variations',
        'product_type',
        'variationInfos',
    ];
    protected $casts = [
        'meta' => 'array',
        'search' => 'array',
        // 'pos_meta' => 'array',
        'notification' => 'array',
        'default_variation' => 'array',
    ];
    protected $dates = ['publish', 'expire', 'release_date'];

    protected $appends = [
        'max',
        'path',
        'price',
        'previews',
        'fake_price',
        'filter_ids',
        'show_hebrew',
        'creators_ids',
        'categories_ids',
        'personalizations',
        'digital_extensions',
        'linked_subscription_groups',
    ];
    protected $guarded = [];
    protected $hidden = [
        'media',
        'width',
        'height',
        'length',
        'weight',
        'origin',
        'search',
        'expire',
        'publish',
        'pos_meta',
        'label_id',
        'tax_code',
        'var_skus',
        'vendor_id',
        'visibility',
        'cost_price',
        'created_at',
        'updated_at',
        'store_title',
        'system_code',
        'product_type',
        'creators_ids',
        'categories_ids',
        'store_quantity',
        'track_inventory',
        'product_type_id',
        'personalizes_id',
        'website_quantity',
        'popularity_score',
        'recurringSettings',
    ];

    protected function casts(): array
    {
        return [
            'release_date' => 'datetime',
            'publish' => 'datetime',
            'start_date' => 'datetime',
            'end_date' => 'datetime',
        ];
    }

    public function creators()
    {
        return $this->belongsToMany(Creator::class)
            ->withTimestamps();
    }

    public function vendor()
    {
        return $this->belongsTo(Vendor::class)
            ->orderBy('name', 'asc');
    }

    public function linkItems()
    {
        return $this->morphMany(LinkItem::class, 'model');
    }

    public function getFromPriceAttribute()
    {
        $infos = $this->variationInfos;
        $prices = $infos->pluck('price');
        if ($infos->count() > 0 && $prices->max() > $prices->min()) {
            return $prices->min();
        }
        return null;
    }

    public function getLinksAttribute()
    {
        return $this->morphMany(LinkItem::class, 'model')
            ->get()
            ->map(function ($item) {
                return $item->link;
            })
            ->map(function ($link) {
                return $link->paths($this->id);
            })->values();
    }

    public function getSubscriptionsAttribute()
    {
        $group_ids = $this->hasMany(SubscriptionGroupItem::class)->pluck('subscription_group_id');
        return Combination::whereIn('group_id', $group_ids)->get()->map(function ($combination) {
            return [
                'subscription_type_id' => $combination->type_id,
                'subscription_group_id' => $combination->group_id,
                'name' => $combination->type->name,
                'date' => $combination->upcoming_date,
                'string' => $combination->upcoming_string,
                'variation_ids' => $combination->group->items->where('model_type', 'App\VariationInfo')->pluck(
                    'model_id'
                ),
            ];
        });
    }

    public function filters()
    {
        return $this->belongsToMany(FilterItem::class, 'filter_item_product')
            ->withTimestamps();
    }

    public function addOns()
    {
        return $this->hasMany(AddOn::class);
    }

    public function label()
    {
        return $this->belongsTo(Label::class);
    }

    public function getLabel()
    {
        if ($label = $this->label) {
            return [
                'name' => $label->name,
                'color' => $label->color,
            ];
        } elseif ($this->sale_price) {
            return [
                'name' => 'Great Deal',
                'color' => '#C13939',
            ];
        }

        return null;
    }

    public function categories()
    {
        return $this->belongsToMany(Category::class);
    }

    public function product_type()
    {
        return $this->belongsTo(ProductType::class);
    }

    public function personalizes()
    {
        return $this->belongsTo(Personalize::class);
    }

    public function getPersonalizationsAttribute()
    {
        return $this->personalizes ? $this->personalizes->AddPictures() : null;
    }

    public function asociated_purchase()
    {
        return $this->hasOne(AssociatedPurchase::class);
    }

    public function models()
    {
        return $this->morphMany(Discount::class, 'model');
    }

    public function getFilterIdsAttribute()
    {
        return $this->filters->pluck('id');
    }

    public function getPrice()
    {
        return $this->price;
    }

    public function getPreviewsAttribute()
    {
        $previews = collect($this->getMedia('mp3_preview'))
            ->merge($this->getMedia('pdf_preview'))
            ->merge($this->getMedia('video_preview'));
        if (count($previews) > 0) {
            return $previews->map(function ($preview) {
                return [
                    'url' => $preview->getURL(),
                    'type' => $preview->mime_type,
                    'rtl' => optional($preview->custom_properties)['rtl'],
                ];
            });
        }
    }

    public function setFilterIdsAttribute($value)
    {
        return;
    }

    public function setNotificationAttribute()
    {
        return;
    }

    public function getCategoriesIdsAttribute()
    {
        return $this->categories->pluck('id');
    }

    public function setCategoriesIdsAttribute($value)
    {
        return;
    }

    public function getCreatorsIdsAttribute()
    {
        return $this->creators->pluck('id');
    }

    public function setCreatorsIdsAttribute($value)
    {
        return;
    }

    public function applyDiscount($discount)
    {
        $discount = Discount::find($discount->id);
        if ($discount->model_type) {
            $model = $discount->model_type;
            if ($model == 'products') {
                if (in_array($this->id, $discount->model_id)) {
                    return true;
                }
            } else {
                $id = $this->$model;
                if ($id instanceof Collection) {
                    if ($id->intersect($discount->model_id)->count()) {
                        return true;
                    }
                } else {
                    if (in_array($id, $discount->model_id)) {
                        return true;
                    }
                }
            }
            return false;
        }
        return true;
    }

    public function getMediaUrlsAttribute()
    {
        return $this->getMedia('media')->sortBy('order_column')->map(function ($item) {
            if ($item->collection_name == 'media') {
                return [
                    'grid' => $item->getUrl('grid'),
                    'large' => $item->getUrl('large'),
                    'lightbox' => $item->getUrl('lightbox'),
                    'thumbnail' => $item->getUrl('thumbnail'),
                ];
            }
        })->filter()->values();
    }

    public function GetDigitalExtensionsAttribute()
    {
        return $this->media->where('collection_name', 'digital')->first()->custom_properties['extensions'] ?? [];
    }

    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(130)
            ->height(130);
        $this->addMediaConversion('thumbnail')
            ->width(100);
        $this->addMediaConversion('grid')
            ->width(250);
        $this->addMediaConversion('large')
            ->width(500);
        $this->addMediaConversion('lightbox')
            ->width(1500);

        $this->addMediaConversion('swatch')
            ->width(40)
            ->performOnCollections('variations');
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('media');

        $this->addMediaCollection('pdf_preview')
            ->singleFile();

        $this->addMediaCollection('mp3_preview')
            ->singleFile();

        $this->addMediaCollection('video_preview')
            ->singleFile();

        $this->addMediaCollection('digital')
            ->singleFile();

        $this->addMediaCollection('variations');

        $this->addMediaCollection('variation_infos');
    }

    public function variations()
    {
        return $this->morphMany(Variation::class, 'model')->ordered();
    }

    public function variationInfos()
    {
        return $this->hasMany(VariationInfo::class);
    }

    public function getPriceAttribute()
    {
        return $this->sale_price
            ?: $this->online_final_price
                ?: $this->store_price
                    ?: $this->list_price;
    }

    public function getFakePriceAttribute()
    {
        $price = $this->list_price
            ?: $this->store_price
                ?: $this->online_final_price
                    ?: $this->sale_price;
        return $this->price != $price ? $price : null;
    }

    public function getPathAttribute()
    {
        if($this->id && $this->title) {
            return "/products/" . Str::slug($this->title) . "/{$this->id}";
        } else {
            return null;
        }
    }
    // public function getRelatedAttribute()
    // {
    //     return self::active()->inRandomOrder()->limit(12)->get();
    // }
    public function setVariationsAttribute($value)
    {
    }

    public function scopeInactive($query)
    {
        $now = now();
        $query->where(function ($query) use ($now) {
            $query->where('visibility', false)->orWhere('publish', '>=', $now);
        });
        return $query;
    }

    public function scopeActive($query)
    {
        $now = now();

        $query->where('visibility', true);
        $query->where(function ($query) use ($now) {
            $query->where('publish', null)->orWhere('publish', '<', $now);
        });
        return $query;
    }

    public function scopeIsSearchable($query) {
        $query->active()->where(function ($query) {
            return $query
                ->where('store_quantity', '>', 0)
                ->orWhere('website_quantity', '>', 0)
                ->orWhere('item_type', '!=', 'physical')
                ->orWhere('track_inventory', false)
                ->orWhere('var_skus->quantity', '>', 0);
        });
        return $query;
    }

    public function scopeisUnsearchable($query)
    {
        $query->where('visibility', false)
            ->orWhere('publish', '>', now())
            ->orWhere(function ($query) {
                return $query
                    ->where('store_quantity', '<', 1)
                    ->where('website_quantity', '<', 1)
                    ->where('item_type', '=', 'physical')
                    ->where('track_inventory', true);
            });
        return $query;
    }

    public function shouldBeSearchable(): bool
    {
        return $this->searchable;
    }

    public function getSearchableAttribute()
    {
        return $this->active && ($this->max > 0
                || $this->variationInfos->map(function ($var) {
                    return $var->max > 0 && $var->visibility;
                })->contains(true)
            );
    }

    public function updateSearchFeild()
    {
        $product = $this;
        $product->withoutEvents(function () use ($product) {
            $product->update([
                'slug' => Str::slug($product->title),
            ]);
            $product->update([
                'search' => $product->getModelSwiftypeTransformed()
            ]);
        });
        $product->variationInfos->each->updateProductJson();
    }

    public function getShowHebrewAttribute()
    {
        if ($this->heb_title || $this->heb_description || $this->heb_short_desc) {
            return true;
        }
        return false;
    }

    public function setShowHebrewAttribute()
    {
        return false;
    }

    public function getActiveAttribute()
    {
        if ($this->visibility && (!$this->publish || $this->publish < now())) {
            return true;
        }
        return false;
    }

    public function getMaxAttribute()
    {
        $max = $this->track_inventory && $this->item_type == 'physical'
            ? max($this->store_quantity + $this->website_quantity, 0)
            : 999;

        $max_quantity = $this->max_quantity;
        if ($max_quantity !== null) {
            return min($max_quantity, $max);
        }

        return $max;
    }

    public function getPreviewAttribute()
    {
        return $this->media()->where('collection_name', 'like', '%_preview')->exists();
    }

    public function getBreadcrumbsAttribute()
    {
        $array = [['name' => $this->title]];
        if ($category = $this->categories->whereIn(
            'id',
            MenuItem::where('class', 'App\Classes\Categories')->pluck('value')
        )->first()) {
            $item = MenuItem::where('value', $category->id)->where('class', 'App\Classes\Categories')->first();
            $array[] = ['name' => $item->name, 'path' => $item->customValue];
            $this->addParent($item, $array);
        } elseif ($category = $this->categories->first()) {
            $array[] = ['name' => $category->name, 'path' => $category->path];
        }
        return array_reverse($array);
    }

    private function addParent($item, &$array)
    {
        if ($item->parent_id) {
            if ($parent = MenuItem::find($item->parent_id)) {
                $array[] = ['name' => $parent->name, 'path' => $parent->customValue];
                $this->addParent($parent, $array);
            }
        }
    }

    public function getFrontEndAddOnsAttribute()
    {
        return $this->addOns->map(function ($addOn) {
            if (!$model = $addOn->model) {
                $addOn->delete();
                return;
            }
            return $addOn->model->getFrontEndAttribute();
        })->filter()->values();
    }

    public function getFormattedAddOnsAttribute()
    {
        return $this->addOns->map(function ($addOn) {
            if (!$model = $addOn->model) {
                $addOn->delete();
                return;
            }
            $data = $addOn->model->getFrontEndAttribute();
            $return = [
                'id' => $addOn->model->id,
                'type' => $data['type'],
                'title' => $addOn->model->title,
                'price' => $addOn->model->toArray()['price'],
                // 'media' => $addOn->model->search['image'],
                'sku' => $addOn->model->sku,
            ];
            if ($data['type'] == 'variation') {
                $return['meta'] = json_decode($addOn->model->meta);
                $return['title'] = $addOn->model->product->title;
                $return['media'] = $addOn->model->product->search['image'];
            } else {
                $return['media'] = $addOn->model->search['image'];
            }
            return $return;
        })->filter()->values();
    }

    public function setFormattedAddOnsAttribute($value)
    {
        $ids = [];
        $addons = collect(json_decode($value))->map(function ($add) use (&$ids) {
            switch ($add->type) {
                case 'product':
                    $type = 'App\Product';
                    break;

                case 'variation':
                    $type = 'App\VariationInfo';
                    break;
            }
            $ids[] = AddOn::firstOrCreate([
                'model_type' => $type,
                'model_id' => $add->id,
                'product_id' => $this->id
            ])->id;
        });
        $this->addOns()->whereNotIn('id', $ids)->delete();
    }

    public function getFrontEndAttribute($quantity = 1)
    {
        $media = optional($this->getMedia('media')->sortBy('order_column')->first());
        $data = [
            'id' => $this->id,
            'type' => 'product',
            'max' => $this->max,
            'sku' => $this->sku,
            'path' => $this->path,
            'price' => $this->price,
            'title' => $this->title,
            'quantity' => $quantity,
            'weight' => $this->weight,
            'product_id' => $this->id,
            'tax_code' => $this->tax_code,
            'item_type' => $this->item_type,
            'media' => $media->getUrl('grid'),
            'total' => $this->price * $quantity,
            'large_media' => $media->getUrl('large'),
            'extentions' => $this->digital_extensions,
            'vendor' => optional($this->vendor)->is_visible ? $this->vendor->name : null,
            'exclude_from_returns' => $this->exclude_from_returns,
            'exclude_free_shipping' => $this->exclude_free_shipping,
            'categories_string' => collect(data_get($this, 'search.categories'))->implode(', '),
        ];
        if ($data['item_type'] === 'digital') {
            $data['file_format'] = $this->file_format;
        }
        $data['download'] = '/api' . $this->path . '/download';
        return $data;
    }

    public function getFollowersAttribute()
    {
        $collection = collect();
        if ($this->creators->count() > 0) {
            $collection = $collection->merge($this->creators->map->followers);
        }
        if ($this->vendor) {
            $collection = $collection->merge($this->vendor->followers);
        }
        return $collection->flatten(1);
    }

    public function getBillableWeightAttribute()
    {
        $dimensional_weight = $this->width
            * $this->height
            * $this->length
            / (settings()->getValue('dimensional_factor') ?? 139);
        if ($this->boxes > 1) {
            $dimensional_weight = $dimensional_weight * $this->boxes;
        }

        $weight = $this->boxes > 1
            ? ($this->weight ? $this->weight : 0) * $this->boxes
            : $this->weight;


        return max([
            $dimensional_weight,
            $weight
        ]);
    }

    public function getProductIdAttribute()
    {
        return $this->id;
    }

    public function getShippableAttribute()
    {
        return data_get($this, 'item_type') == 'physical' || data_get($this, 'item_type') == 'both';
    }

    public function deductQuantity($quantity)
    {
        $deduct_from_website_quantity = $this->getDeductQuantity($quantity)['website_deduct'];
        $deduct_from_store_quantity = $this->getDeductQuantity($quantity)['store_deduct'];

        $this->withoutEvents(function () use ($deduct_from_store_quantity, $deduct_from_website_quantity) {
            $this->update([
                'website_quantity' => $this->website_quantity - $deduct_from_website_quantity,
                'store_quantity' => $this->store_quantity - $deduct_from_store_quantity
            ]);
        });
        Cache::forget("product_{$this->id}");
    }

    public function getDeductQuantity($quantity)
    {
        $deduct_from_website_quantity = min($quantity, $this->website_quantity);
        $deduct_from_store_quantity = $quantity - $deduct_from_website_quantity; // min(max($quantity - $this->website_quantity, 0), $this->store_quantity);

        return [
            'website_deduct' => max($deduct_from_website_quantity, 0),
            'store_deduct' => $deduct_from_store_quantity
        ];
    }

    public function getExtendedDuration()
    {
        $extendedDurtion = 0;
        $product = $this;
        $extendedDurtion += $product->duration ? ($product->duration + 1) : 0;
        if ($product->release_date) {
            $extendedDurtion += now()->isBefore($product->release_date) ? (now()->diffInDays(
                    $product->release_date
                ) + 1) : 0;
        }
        return $extendedDurtion;
    }

    public function removeFromBags()
    {
        $bags = Bag::where([
            'model_type' => get_class($this),
            'model_id' => $this->id
        ])->orderByDesc('updated_at');

        $remove = $bags->sum('quantity') - $this->max;

        $bags->each(function ($bag) use (&$remove) {
            if ($remove >= 1) {
                $deduct = min($bag->quantity, $remove);

                $bag->update([
                    'quantity' => $bag->quantity - $deduct,
                    'active' => $bag->quantity - $deduct > 0
                ]);

                $remove = $remove - $deduct;
            }
        });
    }

    public function getTrackLinkattribute()
    {
        $filter = base64_encode(json_encode([
            [
                'class' => 'App\Nova\Filters\InventorySearch',
                'value' => $this->sku,
            ]
        ]));
        return '/admin/resources/inventory-tracks?inventory-tracks_page=1&inventory-tracks_filter=' . $filter;
    }

    public function setTrackLinkattribute()
    {
    }

    public function getModelSwiftypeTransformed()
    {
        $images = $this->getMedia('variations');
        $variations = $this->variations;
        $sales_last_90_days = ProductSalesService::getSalesForProduct($this->id, now()->subDays(90), now())['quantity'];
        return collect($this->getAttributes())->only([
            'id',
            'title',
            'heb_title',
            'item_type',
        ])->merge([
            'path' => $this->path,
            'price' => $this->price,
            'sale_price' => $this->sale_price,
            'label' => $this->getLabel(),
            'from_price' => $this->from_price,
            'fake_price' => $this->fake_price,
            'vendor' => optional($this->vendor)->is_visible ? $this->vendor->name : null,
            'short_desc' => strip_tags($this->short_desc ?? ''),
            'description' => mb_substr(strip_tags($this->description ?? ''), 0, 1000),
            'image' => $this->getFirstMediaUrl('media', 'grid'),
            'heb_short_desc' => strip_tags($this->heb_short_desc ?? ''),
            'heb_description' => strip_tags($this->heb_description ?? ''),
            'release_date' => optional($this->release_date)->timestamp,
            'sku' => implode(
                ',',
                $this->variationinfos->pluck('sku')
                    ->merge($this->sku)->unique()->filter()->toArray()
            ),
            'barcode' => implode(
                ',',
                $this->variationinfos->pluck('barcode')
                    ->merge($this->barcode)->unique()->filter()->toArray()
            ),
            'meta' => $this->meta ? collect($this->meta)
                ->filter(function ($item) {
                    return !!$item;
                })
                ->map(function ($value, $key) {
                    return $key . '_' . $value;
                })
                ->values() : null,
            'creators' => $this->creators->count() > 0 ? $this->creators->map(function ($creator) {
                return [$creator->name, $creator->heb_name];
            })->flatten(1)->filter()->values() : null,
            'tags' => $this->tags->count() > 0 ? $this->tags->map(function ($tag) {
                return $tag->name;
            }) : null,
            'filters' => $this->filters->count() > 0 ? collect($this->filters)->map(function ($filter) {
                // if ($filter->filter && $filter->filter->show) {
                return $filter->filter->name . '_' . $filter->name;
                // return $value['parent'] . '_' . $value['name'];
                // }
            })->filter()->flatten() : null,
            'questions' => $this->filters->count() > 0 ? collect($this->filters)->map(function ($value) {
                if (!$value->filter || !$value->filter->assistant) {
                    return;
                }
                return $value->filter['name'] . '_' . $value->filter['info'];
            })->filter()->flatten()->unique() : null,
            'categories' => $this->categories->count() > 0 ? $this->categories->map(function ($category) {
                return $category->name;
            }) : null,
            'side_variations' => count($this->variationInfos) > 0 ? collect($this->variationInfos)
                ->map(function ($value, $key) use ($variations) {
                    if (!$value->visibility) {
                        return null;
                    }
                    return collect(json_decode($value->meta))->map(function ($v, $k) use ($variations) {
                        if (!optional($variations->where('name', $k)->first())->filter) {
                            return;
                        }
                        return $k . '_' . $v;
                    })->filter()->values();
                })->filter()->flatten() : null,
            'variations' => count($this->variationInfos) > 0 ? collect($this->variationInfos)
                ->map(function ($value, $key) {
                    if (!$value->visibility) {
                        return null;
                    }
                    return collect(json_decode($value->meta))->map(function ($v, $k) {
                        return $k . '_' . $v;
                    })->values();
                })->filter()->flatten() : null,
            'variation_images' => count($this->variations) > 0 ? collect($this->variations)->map(
                function ($variation) use ($images) {
                    if ($variation->images) {
                        return [
                            'name' => $variation->name,
                            'images' => collect($variation->images)->map(function ($item) use ($images) {
                                return [
                                    'value' => $item['name'],
                                    'url' => optional(
                                        $images->filter(function ($value) use ($item) {
                                            return data_get($value['custom_properties'], '_id') == $item['id'];
                                        })->first()
                                    )->getUrl('grid'),
                                ];
                            })->filter(function ($item) {
                                return $item['value'] && $item['url'];
                            })->values()
                        ];
                    }
                }
            )->filter() : null,
            'sales_last_90_days' => $sales_last_90_days,
        ]);
    }

    public function refreshCache()
    {
        Cache::forget("product_{$this->id}");
        (new ProductObserver)->updateSwiftype($this);
    }

    public function setCategoryIdsAttribute($ids)
    {
        request()->merge(['category_ids' => $ids]);
    }

    public function getLinkedSubscriptionGroupsAttribute()
    {
        return DB::table('subscription_group_items')
            ->where('product_id', $this->id)
            ->pluck('subscription_group_id')->unique();
    }

    public function toSearchableArray()
    {
        return collect($this->search)->merge([
            'release_date' => optional($this->release_date)->timestamp,
        ])->toArray();
        $images = $this->getMedia('variations');
        $variations = $this->variations;
        return [
            'id' => $this->id,
            'title' => $this->title,
            'heb_title' => $this->heb_title,
            'item_type' => $this->item_type,
            'path' => $this->path,
            'price' => $this->price,
            'sale_price' => $this->sale_price,
            'label' => $this->getLabel(),
            'from_price' => $this->from_price,
            'fake_price' => $this->fake_price,
            'vendor' => optional($this->vendor)->is_visible ? $this->vendor->name : null,
            'short_desc' => strip_tags($this->short_desc),
            'description' => substr(strip_tags($this->description), 0, 1000),
            'image' => $this->getFirstMediaUrl('media', 'grid'),
            'heb_short_desc' => strip_tags($this->heb_short_desc),
            'heb_description' => strip_tags($this->heb_description),
            'release_date' => optional($this->release_date)->timestamp,
            'sku' => implode(
                ',',
                $this->variationinfos->pluck('sku')
                    ->merge($this->sku)->unique()->filter()->toArray()
            ),
            'barcode' => implode(
                ',',
                $this->variationinfos->pluck('barcode')
                    ->merge($this->barcode)->unique()->filter()->toArray()
            ),
            'meta' => $this->meta ? collect($this->meta)
                ->filter(function ($item) {
                    return !!$item;
                })
                ->map(function ($value, $key) {
                    return $key . '_' . $value;
                })
                ->values() : null,
            'creators' => $this->creators->count() > 0 ? $this->creators->map(function ($creator) {
                return [$creator->name, $creator->heb_name];
            })->flatten(1)->filter()->values() : null,
            'tags' => $this->tags->count() > 0 ? $this->tags->map(function ($tag) {
                return $tag->name;
            }) : null,
            'filters' => $this->filters->count() > 0 ? collect($this->filters)->map(function ($filter) {
                // if ($filter->filter && $filter->filter->show) {
                return $filter->filter->name . '_' . $filter->name;
                // return $value['parent'] . '_' . $value['name'];
                // }
            })->filter()->flatten() : null,
            'questions' => $this->filters->count() > 0 ? collect($this->filters)->map(function ($value) {
                if (!$value->filter || !$value->filter->assistant) {
                    return;
                }
                return $value->filter['name'] . '_' . $value->filter['info'];
            })->filter()->flatten()->unique() : null,
            'categories' => $this->categories->count() > 0 ? $this->categories->map(function ($category) {
                return $category->name;
            }) : null,
            'side_variations' => count($this->variationInfos) > 0 ? collect($this->variationInfos)
                ->map(function ($value, $key) use ($variations) {
                    if (!$value->visibility) {
                        return null;
                    }
                    return collect(json_decode($value->meta))->map(function ($v, $k) use ($variations) {
                        if (!optional($variations->where('name', $k)->first())->filter) {
                            return;
                        }
                        return $k . '_' . $v;
                    })->filter()->values();
                })->filter()->flatten() : null,
            'variations' => count($this->variationInfos) > 0 ? collect($this->variationInfos)
                ->map(function ($value, $key) {
                    if (!$value->visibility) {
                        return null;
                    }
                    return collect(json_decode($value->meta))->map(function ($v, $k) {
                        return $k . '_' . $v;
                    })->values();
                })->filter()->flatten() : null,
            'variation_images' => count($this->variations) > 0 ? collect($this->variations)->map(
                function ($variation) use ($images) {
                    if ($variation->images) {
                        return [
                            'name' => $variation->name,
                            'images' => collect($variation->images)->map(function ($item) use ($images) {
                                return [
                                    'value' => $item['name'],
                                    'url' => optional(
                                        $images->filter(function ($value) use ($item) {
                                            return data_get($value['custom_properties'], '_id') == $item['id'];
                                        })->first()
                                    )->getUrl('grid'),
                                ];
                            })->filter(function ($item) {
                                return $item['value'] && $item['url'];
                            })->values()
                        ];
                    }
                })->filter() : null,
            ];
    }
}

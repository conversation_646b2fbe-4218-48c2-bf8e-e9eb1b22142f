<script>

export default {
    name: 'EditVariation',
    props: {
        variations: Object,
        media: Array
    },
    data() {
        return {
            show: false,
            currentVariation: {
                image: null,
                file: null,
                store_title: '',
                cost_price: null,
                list_price: null,
                store_price: null,
                add_online_price: '',
                online_price: null,
                online_price_based_on: null,
                online_price_percent: null,
                sale_price: null,
                add_sale: false,
                sale_type: '',
                sale_from: '',
                sale_amount: null,
                start_sale: null,
                end_sale: null,
                sku: null,
                barcode: null,
                gtni: null,
                track_inventory: true,
                store_quantity: null,
                website_quantity: null,
                max_quantity: null,
                item_type: '',
                width: null,
                height: null,
                length: null,
                weight: null,
                boxes: null,
                visibility: true
            }
        }
    },
    methods: {
        open(variation) {
            console.log(variation)
            this.show = true;
            document.body.style.overflow = 'hidden';
            this.currentVariation = variation;
            this.currentVariation.add_sale = Boolean(this.currentVariation.add_sale);
            this.currentVariation.track_inventory = Boolean(this.currentVariation.track_inventory);
            this.currentVariation.visibility = Boolean(this.currentVariation.visibility);
        },
        close() {
            this.save();
            this.show = false;
            document.body.style.overflow = 'auto';
        },
        save() {
            this.$emit('save', this.currentVariation);
        },
        selectVariation(variation) {
            console.log(variation)
            this.save();
            this.currentVariation = variation;
            this.currentVariation.add_sale = Boolean(this.currentVariation.add_sale);
            this.currentVariation.track_inventory = Boolean(this.currentVariation.track_inventory);
            this.currentVariation.visibility = Boolean(this.currentVariation.visibility);
        },
        uploadImage(event) {
            const file = event.target.files[0];
            this.$emit('uploadImage', file);
        },
        selectImage(imageUrl) {
            if (this.currentVariation.image === imageUrl) {
                this.currentVariation.image = null;
                return;
            }
            this.currentVariation.image = imageUrl;
        }
    },
}

</script>

<template>
    <teleport to="body">
        <div v-if="show" class="edit-variation edit-variation-wrapper">
            <div class="edit-variation-inner">
                <div class="edit-variation-header">
                    <h2 class="text-lg font-semibold">Update variations</h2>
                    <div class="edit-variation-actions">
                        <outline-button @click="close">Cancel</outline-button>
                        <default-button @click="close">Done</default-button>
                    </div>
                </div>
                <div class="edit-variation-content">
                    <div class="edit-variation-sidebar">
                        <h3>Variations</h3>
                        <button v-for="variation in variations" @click="selectVariation(variation)" class="btn-sidebar"
                                :class="{ 'active': currentVariation.id === variation.id }">{{ variation.store_title }}
                        </button>
                    </div>
                    <div class="edit-variation-form">
                        <div class="edit-variation-panel">
                            <h3>Media</h3>
                            <div class="panel-content py-6">
                                <div class="flex items-center gap-2">
                                    <div v-for="image in media" :key="image.uuid"
                                         :class="[ currentVariation.image === (image.original_url ?? image.uuid) ? 'border-primary-500' : 'border-transparent' ]"
                                         @click="selectImage(image.original_url ?? image.uuid)"
                                         class="relative w-25 h-25 cursor-pointer rounded border-4 transition-colors hover:border-primary-500">
                                        <img :src="image.original_url ?? image.preview"
                                             class="w-full h-full object-cover"/>
                                        <div type="button" class="remove-icon"
                                             @click.prevent="$emit('removeImage', image.uuid)">
                                            <Icon type="x"/>
                                        </div>
                                    </div>
                                    <div class="relative w-25 h-25 flex items-center justify-center">
                                        <Icon type="plus-circle" class="hover:opacity-50 w-14 h-14 cursor-pointer"
                                              @click.prevent="$refs[`v_file`].click()"/>
                                        <input :ref="`v_file`" type="file" class="hidden"
                                               @change="uploadImage($event)"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="edit-variation-panel">
                            <h3>Pricing</h3>
                            <div class="panel-content">
                                <div class="form-field">
                                    <label>List Price</label>
                                    <div class="form-control-wrap">
                                        <span>$</span>
                                        <input
                                            v-model="currentVariation.list_price"
                                            placeholder="Enter List Price"
                                            type="number"
                                            step=".01"
                                        />
                                    </div>
                                </div>
                                <div class="form-field">
                                    <label>Store Price</label>
                                    <div class="form-control-wrap">
                                        <span>$</span>
                                        <input
                                            disabled
                                            :value="currentVariation.store_price"
                                            placeholder="Enter Store Price"
                                            type="number"
                                            step=".01"
                                        />
                                    </div>
                                </div>
                                <div class="form-field">
                                    <label>Online Price</label>
                                    <div class="form-control-wrap">
                                        <select v-model="currentVariation.add_online_price">
                                            <option value="">Choose Option</option>
                                            <option value="set">Set Price</option>
                                            <option value="rules">Pricing Rule</option>
                                        </select>
                                    </div>
                                </div>
                                <div v-if="currentVariation.add_online_price === 'set'" class="form-field">
                                    <label>Online Price</label>
                                    <div class="form-control-wrap">
                                        <span>$</span>
                                        <input
                                            v-model="currentVariation.online_price"
                                            placeholder="Enter Online Price"
                                            type="number"
                                            step=".01"
                                        />
                                    </div>
                                </div>
                                <div v-if="currentVariation.add_online_price === 'rules'" class="form-field">
                                    <label>Based On</label>
                                    <div class="form-control-wrap">
                                        <select v-model="currentVariation.online_price_based_on">
                                            <option value="">Choose Option</option>
                                            <option value="list_price">List Price</option>
                                            <option value="store_price">Store Price</option>
                                            <option value="online_price">Online Price</option>
                                            <option value="sale_price">Store Sale Price</option>
                                            <option value="cost_price">Cost Price</option>
                                        </select>
                                    </div>
                                </div>
                                <div v-if="currentVariation.add_online_price === 'rules'" class="form-field">
                                    <label>Percent</label>
                                    <div class="form-control-wrap">
                                        <input
                                            v-model="currentVariation.online_price_percent"
                                            placeholder="Enter Percent"
                                            type="number"
                                            step=".01"
                                        />
                                        <span>%</span>
                                    </div>
                                </div>
                                <div class="form-field">
                                    <label>Store Sale Price</label>
                                    <div class="form-control-wrap">
                                        <span>$</span>
                                        <input
                                            disabled
                                            :value="currentVariation.sale_price"
                                            placeholder="Enter Store Sale Price"
                                            type="number"
                                            step=".01"
                                        />
                                    </div>
                                </div>
                                <div class="form-field">
                                    <label>Online Sale Price</label>
                                    <div class="form-control-wrap">
                                        <label class="switch">
                                            <input
                                                v-model="currentVariation.add_sale"
                                                type="checkbox"
                                            />
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                </div>
                                <div v-if="currentVariation.add_sale" class="form-field">
                                    <label>Sale Type</label>
                                    <div class="form-control-wrap">
                                        <select v-model="currentVariation.sale_type">
                                            <option value="fixed">Fixed</option>
                                            <option value="percent">Percent Off</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-field"
                                     v-if="currentVariation.add_sale && currentVariation.sale_type === 'percent'">
                                    <label>Deduct From</label>
                                    <div class="form-control-wrap">
                                        <select v-model="currentVariation.sale_from">
                                            <option value="list_price">List Price</option>
                                            <option value="store_price">Store Price</option>
                                            <option value="online_price">Online Price</option>
                                            <option value="sale_price">Sale Price</option>
                                            <option value="cost_price">Cost Price</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-field"
                                     v-if="currentVariation.add_sale && currentVariation.sale_type === 'percent'">
                                    <label>Percent</label>
                                    <div class="form-control-wrap">
                                        <input
                                            v-model="currentVariation.sale_amount"
                                            placeholder="Enter Amount"
                                            type="number"
                                            step=".01"
                                        />
                                        <span>%</span>
                                    </div>
                                </div>
                                <div class="form-field"
                                     v-if="currentVariation.add_sale && currentVariation.sale_type === 'fixed'">
                                    <label>Sale Price</label>
                                    <div class="form-control-wrap">
                                        <span>$</span>
                                        <input
                                            v-model="currentVariation.sale_price"
                                            placeholder="Enter Fixed Amount"
                                            type="number"
                                            step=".01"
                                        />
                                    </div>
                                </div>
                                <div v-if="currentVariation.add_sale" class="form-field">
                                    <label>Start Sale</label>
                                    <div class="form-control-wrap">
                                        <input type="date" v-model="currentVariation.start_sale"/>
                                    </div>
                                </div>
                                <div v-if="currentVariation.add_sale" class="form-field">
                                    <label>End Sale</label>
                                    <div class="form-control-wrap">
                                        <input type="date" v-model="currentVariation.end_sale"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="edit-variation-panel">
                            <h3>Inventory</h3>
                            <div class="panel-content">
                                <div class="form-field">
                                    <label>SKU Number</label>
                                    <div class="form-control-wrap">
                                        <input
                                            v-model="currentVariation.sku"
                                            placeholder="Enter SKU Number"
                                            type="text"
                                        />
                                    </div>
                                </div>
                                <div class="form-field">
                                    <label>Barcode</label>
                                    <div class="form-control-wrap">
                                        <input
                                            v-model="currentVariation.barcode"
                                            placeholder="Enter Barcode"
                                            type="text"
                                        />
                                    </div>
                                </div>
                                <div class="form-field">
                                    <label>GTIN</label>
                                    <div class="form-control-wrap">
                                        <input
                                            v-model="currentVariation.gtni"
                                            placeholder="Enter GTIN"
                                            type="text"
                                        />
                                    </div>
                                </div>
                                <div class="form-field">
                                    <label>Track Inventory</label>
                                    <div class="form-control-wrap">
                                        <label class="switch">
                                            <input
                                                v-model="currentVariation.track_inventory"
                                                type="checkbox"
                                            />
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="form-field" v-if="currentVariation.track_inventory">
                                    <label>Store Quantity</label>
                                    <div class="form-control-wrap">
                                        <input
                                            disabled
                                            v-model="currentVariation.store_quantity"
                                            placeholder="Enter Store Quantity"
                                            type="number"
                                        />
                                    </div>
                                </div>
                                <div class="form-field" v-if="currentVariation.track_inventory">
                                    <label>Fulfillment Quantity</label>
                                    <div class="form-control-wrap">
                                        <input
                                            disabled
                                            v-model="currentVariation.website_quantity"
                                            placeholder="Enter Website Quantity"
                                            type="number"
                                        />
                                    </div>
                                </div>
                                <div class="form-field">
                                    <label>Max Quantity</label>
                                    <div class="form-control-wrap">
                                        <input
                                            v-model="currentVariation.max_quantity"
                                            placeholder="Enter Max Quantity"
                                            type="number"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="edit-variation-panel">
                            <h3>Shipping</h3>
                            <div class="panel-content">
                                <div class="form-field">
                                    <label>Item Type</label>
                                    <div class="form-control-wrap">
                                        <select v-model="currentVariation.item_type">
                                            <option value="">Choose Option</option>
                                            <option value="physical">Physical Item</option>
                                            <option value="digital">Digital Item</option>
                                            <option value="both">Physical & Digital Item</option>
                                            <option value="service">Service Item</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-field">
                                    <label>Width</label>
                                    <div class="form-control-wrap">
                                        <input
                                            v-model="currentVariation.width"
                                            placeholder="Enter Width"
                                            type="number"
                                            step=".01"
                                        />
                                        <span>in</span>
                                    </div>
                                </div>
                                <div class="form-field">
                                    <label>Height</label>
                                    <div class="form-control-wrap">
                                        <input
                                            v-model="currentVariation.height"
                                            placeholder="Enter Height"
                                            type="number"
                                            step=".01"
                                        />
                                        <span>in</span>
                                    </div>
                                </div>
                                <div class="form-field">
                                    <label>Length</label>
                                    <div class="form-control-wrap">
                                        <input
                                            v-model="currentVariation.length"
                                            placeholder="Enter Length"
                                            type="number"
                                            step=".01"
                                        />
                                        <span>in</span>
                                    </div>
                                </div>
                                <div class="form-field">
                                    <label>Weight</label>
                                    <div class="form-control-wrap">
                                        <input
                                            v-model="currentVariation.weight"
                                            placeholder="Enter Weight"
                                            type="number"
                                            step=".01"
                                        />
                                        <span>lb</span>
                                    </div>
                                </div>
                                <div class="form-field">
                                    <label>Amount Of Boxes</label>
                                    <div class="form-control-wrap">
                                        <input
                                            v-model="currentVariation.boxes"
                                            placeholder="Enter Boxes"
                                            type="number"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="edit-variation-panel">
                            <h3>Variation Visibility</h3>
                            <div class="panel-content">
                                <div class="form-field">
                                    <label>Visibility</label>
                                    <div class="form-control-wrap">
                                        <label class="switch">
                                            <input
                                                v-model="currentVariation.visibility"
                                                type="checkbox"
                                            />
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </teleport>
</template>

<style lang="scss" scoped>
.edit-variation {
    .w-25 {
        width: 6.25rem;
    }

    .h-25 {
        height: 6.25rem;
    }

    &-wrapper {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        justify-content: flex-end;
        z-index: 10000;
        background-color: rgba(124, 133, 142, .7);
        align-items: flex-start;
        overflow-y: auto;
    }

    &-inner {
        background-color: rgb(var(--colors-white));
        border-radius: 0.5rem 0 0 0.5rem;
        width: 80%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    &-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem;
        background: rgb(var(--colors-slate-200));
        border-top-left-radius: 0.5rem;
        border-bottom: 2px solid rgb(var(--colors-slate-300));
        position: sticky;
        top: 0;
    }

    &-actions {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    &-content {
        display: flex;
        justify-content: space-between;
        flex-grow: 1;
    }

    &-sidebar {
        width: 20%;
        background: rgb(var(--colors-slate-100));
        border-right: 1px solid rgb(var(--colors-slate-200));
        flex-shrink: 0;

        h3 {
            padding: 1.4rem;
            color: rgb(var(--colors-slate-600));
            font-weight: bold;
            font-size: 1rem;
        }

        .btn-sidebar {
            display: block;
            width: 100%;
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid rgb(var(--colors-slate-300));
            background: rgb(var(--colors-slate-100));
            color: rgb(var(--colors-slate-600));

            &:hover {
                background: rgb(var(--colors-slate-200));
            }

            &.active {
                background: rgb(var(--colors-slate-400));
                color: rgb(var(--colors-white));
            }
        }
    }

    &-form {
        flex-grow: 1;
        padding: 1rem;
        background: rgb(var(--colors-slate-50));
    }

    &-panel {
        margin: 1.5rem 0;

        h3 {
            color: rgb(var(--colors-slate-600));
            font-weight: bold;
            font-size: 1rem;
            margin-bottom: 1rem;
        }

        .panel-content {
            background: rgb(var(--colors-white));
            border-radius: 0.5rem;
        }
    }

    .form-field {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 1rem;

        &:not(:last-child) {
            border-bottom: 1px solid rgb(var(--colors-slate-200));
        }

        label {
            color: rgb(var(--colors-slate-600));
            font-weight: bold;
            font-size: 0.875rem;
            width: 25%;
            flex-shrink: 0;
        }

        .form-control-wrap {
            display: flex;
            flex-grow: 1;

            span {
                display: flex;
                align-items: center;
                padding: 0.5rem 0.8rem;
                background: rgb(var(--colors-slate-100));
                border: 1px solid rgb(var(--colors-slate-200));
                color: rgb(var(--colors-slate-600));
                font-size: 1rem;

                &:first-child {
                    border-top-left-radius: 0.5rem;
                    border-bottom-left-radius: 0.5rem;
                }

                &:last-child {
                    border-top-right-radius: 0.5rem;
                    border-bottom-right-radius: 0.5rem;
                }
            }

            input:not([type="checkbox"]), select {
                flex-grow: 1;
                border: 1px solid rgb(var(--colors-slate-300));
                font-size: 1rem;
                padding: 0.5rem;

                &:first-child {
                    border-top-left-radius: 0.5rem;
                    border-bottom-left-radius: 0.5rem;
                }

                &:last-child {
                    border-top-right-radius: 0.5rem;
                    border-bottom-right-radius: 0.5rem;
                }

                &:focus {
                    border: 1px solid rgb(var(--colors-primary-500));
                }
            }
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 46px;
            height: 26px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(var(--colors-gray-200));
            transition: 0.4s;
            border-radius: 20px !important;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: 0.4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: rgba(var(--colors-primary-500));
        }

        input:checked + .slider:before {
            transform: translateX(20px);
        }
    }
}
</style>
